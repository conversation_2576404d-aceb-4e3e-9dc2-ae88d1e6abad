#!/usr/bin/env python3
"""
测试修复后的Mamba实现
验证选择性扫描算法的正确性
"""
import torch
import torch.nn as nn
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.modules.mamba_core import SelectiveScan, BidirectionalMamba, LocalMamba, GeometricReorder


def test_selective_scan_basic():
    """测试SelectiveScan的基本功能"""
    print("=" * 50)
    print("测试 SelectiveScan 基本功能")
    print("=" * 50)
    
    # 创建测试数据
    B, L, D = 2, 10, 64  # 批次大小, 序列长度, 特征维度
    d_state = 16
    
    # 创建模块
    ssm = SelectiveScan(d_model=D, d_state=d_state, d_conv=4, expand=2)
    
    # 创建输入
    x = torch.randn(B, L, D)
    
    print(f"输入形状: {x.shape}")
    
    try:
        # 前向传播
        output = ssm(x)
        print(f"输出形状: {output.shape}")
        print(f"形状匹配: {x.shape == output.shape}")
        
        # 检查输出的数值特性
        print(f"输出均值: {output.mean().item():.6f}")
        print(f"输出标准差: {output.std().item():.6f}")
        print(f"输出范围: [{output.min().item():.6f}, {output.max().item():.6f}]")
        
        # 检查是否有NaN或Inf
        has_nan = torch.isnan(output).any()
        has_inf = torch.isinf(output).any()
        print(f"包含NaN: {has_nan}")
        print(f"包含Inf: {has_inf}")
        
        if not has_nan and not has_inf:
            print("✅ SelectiveScan 基本功能测试通过")
            return True
        else:
            print("❌ SelectiveScan 输出包含异常值")
            return False
            
    except Exception as e:
        print(f"❌ SelectiveScan 测试失败: {e}")
        return False


def test_gradient_flow():
    """测试梯度流动"""
    print("\n" + "=" * 50)
    print("测试梯度流动")
    print("=" * 50)
    
    B, L, D = 2, 20, 64
    
    ssm = SelectiveScan(d_model=D, d_state=16, d_conv=4, expand=2)
    x = torch.randn(B, L, D, requires_grad=True)
    
    try:
        # 前向传播
        output = ssm(x)
        
        # 计算损失并反向传播
        loss = output.sum()
        loss.backward()
        
        # 检查梯度
        input_grad_norm = x.grad.norm().item()
        print(f"输入梯度范数: {input_grad_norm:.6f}")
        
        # 检查模型参数梯度
        param_grads = []
        for name, param in ssm.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                param_grads.append((name, grad_norm))
                print(f"{name} 梯度范数: {grad_norm:.6f}")
        
        if input_grad_norm > 0 and len(param_grads) > 0:
            print("✅ 梯度流动测试通过")
            return True
        else:
            print("❌ 梯度流动测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 梯度流动测试失败: {e}")
        return False


def test_sequence_lengths():
    """测试不同序列长度的处理"""
    print("\n" + "=" * 50)
    print("测试不同序列长度")
    print("=" * 50)
    
    B, D = 2, 64
    test_lengths = [16, 32, 64, 128, 243]  # 包括TCPFormer常用的243帧
    
    ssm = SelectiveScan(d_model=D, d_state=16, d_conv=4, expand=2)
    
    results = []
    
    for L in test_lengths:
        try:
            x = torch.randn(B, L, D)
            
            # 测试前向传播
            with torch.no_grad():
                output = ssm(x)
            
            # 检查输出
            shape_ok = output.shape == (B, L, D)
            no_nan = not torch.isnan(output).any()
            no_inf = not torch.isinf(output).any()
            
            status = "✅" if (shape_ok and no_nan and no_inf) else "❌"
            print(f"序列长度 {L:3d}: {status} 形状={output.shape} 范围=[{output.min().item():.3f}, {output.max().item():.3f}]")
            
            results.append(shape_ok and no_nan and no_inf)
            
        except Exception as e:
            print(f"序列长度 {L:3d}: ❌ 错误: {e}")
            results.append(False)
    
    success_rate = sum(results) / len(results)
    print(f"\n序列长度测试成功率: {success_rate:.1%}")
    
    return success_rate >= 0.8


def test_bidirectional_mamba():
    """测试双向Mamba"""
    print("\n" + "=" * 50)
    print("测试 BidirectionalMamba")
    print("=" * 50)
    
    B, T, J, C = 2, 81, 17, 128  # TCPFormer输入格式
    
    try:
        # 创建双向Mamba
        bi_mamba = BidirectionalMamba(d_model=C, d_state=16, d_conv=4, expand=2)
        
        # 创建输入
        x = torch.randn(B, T, J, C)
        print(f"输入形状: {x.shape}")
        
        # 前向传播
        output = bi_mamba(x)
        print(f"输出形状: {output.shape}")
        print(f"形状匹配: {x.shape == output.shape}")
        
        # 检查输出质量
        has_nan = torch.isnan(output).any()
        has_inf = torch.isinf(output).any()
        
        if not has_nan and not has_inf and output.shape == x.shape:
            print("✅ BidirectionalMamba 测试通过")
            return True
        else:
            print("❌ BidirectionalMamba 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ BidirectionalMamba 测试失败: {e}")
        return False


def test_geometric_reorder():
    """测试几何重排序"""
    print("\n" + "=" * 50)
    print("测试 GeometricReorder")
    print("=" * 50)
    
    B, T, J, C = 2, 81, 17, 128
    
    try:
        reorder = GeometricReorder(num_joints=J)
        x = torch.randn(B, T, J, C)
        
        # 重排序
        x_reordered = reorder(x)
        print(f"原始形状: {x.shape}")
        print(f"重排序后形状: {x_reordered.shape}")
        
        # 恢复
        x_restored = reorder.restore(x_reordered)
        print(f"恢复后形状: {x_restored.shape}")
        
        # 检查是否完全恢复
        diff = torch.abs(x - x_restored).max().item()
        print(f"恢复误差: {diff}")
        
        if diff < 1e-6:
            print("✅ GeometricReorder 测试通过")
            return True
        else:
            print("❌ GeometricReorder 测试失败")
            return False
            
    except Exception as e:
        print(f"❌ GeometricReorder 测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始测试修复后的Mamba实现")
    print("=" * 60)
    
    tests = [
        ("SelectiveScan基本功能", test_selective_scan_basic),
        ("梯度流动", test_gradient_flow),
        ("不同序列长度", test_sequence_lengths),
        ("双向Mamba", test_bidirectional_mamba),
        ("几何重排序", test_geometric_reorder),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    success_rate = passed / len(results)
    print(f"\n整体成功率: {success_rate:.1%} ({passed}/{len(results)})")
    
    if success_rate >= 0.8:
        print("\n🎉 Mamba实现修复成功！")
    else:
        print("\n⚠️  仍有问题需要解决")
    
    return success_rate >= 0.8


if __name__ == "__main__":
    # 设置随机种子以便重现
    torch.manual_seed(42)
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n建议下一步:")
        print("1. 在实际数据上测试模型")
        print("2. 与原始TCPFormer进行性能对比")
        print("3. 监控训练过程的稳定性")
    else:
        print("\n建议:")
        print("1. 检查失败的测试项")
        print("2. 调试具体的错误信息")
        print("3. 考虑进一步优化实现")