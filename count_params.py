#!/usr/bin/env python3
"""
计算TCPFormer模型参数量的脚本
比较修复前后的参数量变化
"""
import torch
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.Model import MemoryInducedTransformer, MambaInducedTransformer

def count_parameters(model):
    """计算模型参数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def format_number(num):
    """格式化数字显示"""
    if num >= 1e6:
        return f"{num/1e6:.2f}M"
    elif num >= 1e3:
        return f"{num/1e3:.1f}K"
    else:
        return str(num)

def main():
    print("="*60)
    print("TCPFormer 模型参数量统计")
    print("="*60)
    
    # 模型配置
    config = {
        'n_layers': 16,
        'dim_in': 3,
        'dim_feat': 128,
        'dim_rep': 512,
        'dim_out': 3,
        'mlp_ratio': 4,
        'num_heads': 4,
        'num_joints': 17,
        'n_frames': 243,
        'hierarchical': False,
        'use_tcn': False,
        'graph_only': False
    }
    
    print(f"模型配置:")
    print(f"  - 层数: {config['n_layers']}")
    print(f"  - 特征维度: {config['dim_feat']}")
    print(f"  - 关节数: {config['num_joints']}")
    print(f"  - 帧数: {config['n_frames']}")
    print()
    
    # 1. 原始MemoryInducedTransformer（不使用Mamba）
    print("1. 原始MemoryInducedTransformer（无Mamba增强）:")
    try:
        model_original = MemoryInducedTransformer(
            use_mamba_enhanced=False,  # 不使用Mamba
            **config
        )
        total_orig, trainable_orig = count_parameters(model_original)
        print(f"   总参数量: {format_number(total_orig)} ({total_orig:,})")
        print(f"   可训练参数: {format_number(trainable_orig)} ({trainable_orig:,})")
    except Exception as e:
        print(f"   错误: {e}")
    print()
    
    # 2. 带Mamba增强的MemoryInducedTransformer
    print("2. MemoryInducedTransformer（使用Mamba增强）:")
    try:
        model_mamba_enhanced = MemoryInducedTransformer(
            use_mamba_enhanced=True,  # 使用Mamba
            mamba_d_state=16,
            mamba_d_conv=4,
            mamba_expand=2,
            **config
        )
        total_mamba, trainable_mamba = count_parameters(model_mamba_enhanced)
        print(f"   总参数量: {format_number(total_mamba)} ({total_mamba:,})")
        print(f"   可训练参数: {format_number(trainable_mamba)} ({trainable_mamba:,})")
    except Exception as e:
        print(f"   错误: {e}")
    print()
    
    # 3. MambaInducedTransformer（专门的Mamba版本）
    print("3. MambaInducedTransformer（专用Mamba版本）:")
    try:
        model_mamba_dedicated = MambaInducedTransformer(
            mamba_d_state=16,
            mamba_d_conv=4,
            mamba_expand=2,
            **config
        )
        total_dedicated, trainable_dedicated = count_parameters(model_mamba_dedicated)
        print(f"   总参数量: {format_number(total_dedicated)} ({total_dedicated:,})")
        print(f"   可训练参数: {format_number(trainable_dedicated)} ({trainable_dedicated:,})")
    except Exception as e:
        print(f"   错误: {e}")
    print()
    
    # 参数量比较
    print("="*60)
    print("参数量对比分析:")
    print("="*60)
    
    if 'total_orig' in locals() and 'total_mamba' in locals():
        diff_enhanced = total_mamba - total_orig
        ratio_enhanced = total_mamba / total_orig
        print(f"Mamba增强版 vs 原始版本:")
        print(f"  参数增加: {format_number(diff_enhanced)} ({diff_enhanced:+,})")
        print(f"  增长比例: {ratio_enhanced:.2f}x")
        print()
    
    if 'total_orig' in locals() and 'total_dedicated' in locals():
        diff_dedicated = total_dedicated - total_orig
        ratio_dedicated = total_dedicated / total_orig
        print(f"专用Mamba版 vs 原始版本:")
        print(f"  参数增加: {format_number(diff_dedicated)} ({diff_dedicated:+,})")
        print(f"  增长比例: {ratio_dedicated:.2f}x")
        print()
    
    # 测试模型前向传播
    print("="*60)
    print("模型功能测试:")
    print("="*60)
    
    # 创建测试数据
    batch_size = 2
    test_input = torch.randn(batch_size, config['n_frames'], config['num_joints'], config['dim_in'])
    print(f"测试输入形状: {test_input.shape}")
    
    # 测试原始模型
    if 'model_original' in locals():
        try:
            with torch.no_grad():
                output_orig = model_original(test_input)
            print(f"原始模型输出形状: {output_orig.shape}")
        except Exception as e:
            print(f"原始模型测试失败: {e}")
    
    # 测试Mamba增强模型
    if 'model_mamba_enhanced' in locals():
        try:
            with torch.no_grad():
                output_enhanced = model_mamba_enhanced(test_input)
            print(f"Mamba增强模型输出形状: {output_enhanced.shape}")
        except Exception as e:
            print(f"Mamba增强模型测试失败: {e}")
    
    # 测试专用Mamba模型
    if 'model_mamba_dedicated' in locals():
        try:
            with torch.no_grad():
                output_dedicated = model_mamba_dedicated(test_input)
            print(f"专用Mamba模型输出形状: {output_dedicated.shape}")
        except Exception as e:
            print(f"专用Mamba模型测试失败: {e}")
    
    print("\n" + "="*60)
    print("统计完成！")
    print("="*60)

if __name__ == "__main__":
    main()