#Training
learning_rate: 0.0005
batch_size: 4
weight_decay: 0.01
lr_decay: 0.99
epochs: 60
train_2d: False

# Model - 轻量级版本用于快速测试
model_name: MambaInducedTransformer
n_layers: 8  # 减少层数
dim_in: 3
dim_feat: 96  # 减少特征维度
dim_rep: 384  # 减少表示维度
dim_out: 3
mlp_ratio: 4
act_layer: gelu
attn_drop: 0.0
drop: 0.0
drop_path: 0.0
use_layer_scale: True
layer_scale_init_value: 0.00001
use_adaptive_fusion: True
num_heads: 6  # 减少注意力头数
qkv_bias: False
qkv_scale: null
hierarchical: False
use_temporal_similarity: True 
neighbour_num: 2  
temporal_connection_len: 1 
use_tcn: False
graph_only: False
n_frames: 81  # 减少帧数

# Mamba特定参数
mamba_d_state: 16
mamba_d_conv: 4
mamba_expand: 2
use_geometric_reorder: True
use_bidirectional: True
use_local_mamba: True

# Data
data_root: data/motion3d/
data_root_2d: data/motion2d/
subset_list: [ H36M-81 ]
dt_file: h36m_sh_conf_cam_source_final.pkl
num_joints: 17
root_rel: True
add_velocity: False

# Loss weights (使用与原版TCPFormer相同的权重)
lambda_3d_velocity: 20.0
lambda_scale: 0.5
lambda_lv: 0.0
lambda_lg: 0.0
lambda_a: 0.0
lambda_av: 0.0
lambda_mi: 0.001

# Augmentation
use_proj_as_2d: False
flip: True

# Evaluation
eval_joints: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
test_augmentation: True

# Checkpoint and logging - 轻量级版本
checkpoint_dir: checkpoint/mamba_tcp_81/
log_dir: logs/mamba_tcp_81/
save_frequency: 5
eval_frequency: 1

# Wandb settings
use_wandb: True
wandb_project: "MambaTCP-H36M"
wandb_entity: "pose-estimation"
wandb_name: "MambaTCP-81frames-lightweight"
